25-08-22.16:45:05.550 [main            ] INFO  Application            - Starting Application using Java 17.0.14 with PID 43544 (E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app\target\classes started by ChenSir in E:\Java\ai-mcp-knowledge-20602)
25-08-22.16:45:05.551 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-22.16:45:06.862 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.16:45:06.866 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.16:45:06.866 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:45:07.163 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.16:45:07.163 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.16:45:07.163 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:45:07.449 [main            ] INFO  Application            - Started Application in 2.313 seconds (process running for 3.285)
25-08-22.16:45:36.798 [main            ] INFO  Application            - Starting Application using Java 17.0.14 with PID 39484 (E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app\target\classes started by ChenSir in E:\Java\ai-mcp-knowledge-20602)
25-08-22.16:45:36.801 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-22.16:45:38.088 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.16:45:38.092 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.16:45:38.092 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:45:38.358 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.16:45:38.358 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.16:45:38.358 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:45:38.635 [main            ] INFO  Application            - Started Application in 2.334 seconds (process running for 3.264)
