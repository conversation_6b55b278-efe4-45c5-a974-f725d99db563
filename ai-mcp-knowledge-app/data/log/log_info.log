25-08-22.16:47:47.253 [main            ] INFO  OllamaTest             - Starting OllamaTest using Java 17.0.14 with PID 40068 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.16:47:47.255 [main            ] INFO  OllamaTest             - The following 1 profile is active: "dev"
25-08-22.16:47:48.541 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.16:47:48.544 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.16:47:48.544 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:47:48.726 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.16:47:48.727 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.16:47:48.727 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:47:49.092 [main            ] INFO  OllamaTest             - Started OllamaTest in 2.214 seconds (process running for 3.283)
25-08-22.16:47:50.013 [main            ] WARN  OllamaApi              - [404] Not Found - {"error":"model \"deepseek-r1:1.5b\" not found, try pulling it first"}
25-08-22.16:58:52.139 [main            ] INFO  OllamaTest             - Starting OllamaTest using Java 17.0.14 with PID 35436 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.16:58:52.141 [main            ] INFO  OllamaTest             - The following 1 profile is active: "dev"
25-08-22.16:58:53.405 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.16:58:53.408 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.16:58:53.408 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:58:53.587 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.16:58:53.589 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.16:58:53.589 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:58:53.903 [main            ] INFO  OllamaTest             - Started OllamaTest in 2.136 seconds (process running for 2.875)
25-08-22.16:59:12.877 [main            ] INFO  OllamaTest             - Starting OllamaTest using Java 17.0.14 with PID 40080 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.16:59:12.878 [main            ] INFO  OllamaTest             - The following 1 profile is active: "dev"
25-08-22.16:59:14.070 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.16:59:14.072 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.16:59:14.072 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:59:14.248 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.16:59:14.249 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.16:59:14.249 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:59:14.545 [main            ] INFO  OllamaTest             - Started OllamaTest in 2.032 seconds (process running for 2.735)
25-08-22.16:59:15.955 [main            ] INFO  OllamaTest             - 测试结果(call):{"metadata":{"empty":false,"id":"","model":"deepseek-r1:1.5b","rateLimit":{"requestsLimit":0,"requestsRemaining":0,"requestsReset":{"nano":0,"negative":false,"seconds":0,"units":["SECONDS","NANOS"],"zero":true},"tokensLimit":0,"tokensRemaining":0,"tokensReset":{"$ref":"$.metadata.rateLimit.requestsReset"}},"usage":{"promptTokens":6,"completionTokens":14,"totalTokens":20,"generationTokens":14}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"stop"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"<think>\n\n</think>\n\n1 + 1 equals **2**."{"$ref":"$.metadata.rateLimit.requestsReset.usage.result.metadata.contentFilters.output.media"}}},"results":[{"$ref":"$.metadata.rateLimit.requestsReset.usage.result"}]}
25-08-22.16:59:29.432 [main            ] INFO  OllamaTest             - Starting OllamaTest using Java 17.0.14 with PID 43536 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.16:59:29.433 [main            ] INFO  OllamaTest             - The following 1 profile is active: "dev"
25-08-22.16:59:30.694 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.16:59:30.697 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.16:59:30.697 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:59:30.869 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.16:59:30.870 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.16:59:30.870 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.16:59:31.226 [main            ] INFO  OllamaTest             - Started OllamaTest in 2.15 seconds (process running for 2.96)
25-08-22.16:59:44.722 [main            ] INFO  OllamaTest             - 测试结果(images):{"metadata":{"empty":false,"id":"","model":"deepseek-r1:1.5b","rateLimit":{"requestsLimit":0,"requestsRemaining":0,"requestsReset":{"nano":0,"negative":false,"seconds":0,"units":["SECONDS","NANOS"],"zero":true},"tokensLimit":0,"tokensRemaining":0,"tokensReset":{"$ref":"$.metadata.rateLimit.requestsReset"}},"usage":{"promptTokens":23,"completionTokens":474,"totalTokens":497,"generationTokens":474}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"stop"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"<think>\n嗯，用户让我描述一张图片中的主要内容以及物品的用途。我需要先分析图片的内容和结构，然后确定每个物品的具体用途。首先，我要想象或者回忆出这张图片的画面，比如是否有动物、建筑、工具、装饰等元素。\n\n假设这是张家嘴河上的图片，那可能涉及到水中的交通工具。比如竹筏或者竹马，这样的情景比较常见。接下来，我需要确定每一辆车的用途。例如，钓鱼船可能是用来钓鱼的，可能在水中游泳，所以可以用来钓鱼和游泳。竹筏和竹马可能会被带下水游泳，作为钓鱼工具。还有其他物品，比如渔网、渔火之类的，这些可能与 fishing 相关。\n\n然后，我需要按照用户的要求，先描述主要内容，也就是图片的主要元素，比如河流、小岛等。接着详细说明每个物品的用途，特别是它们是如何在主要内容中发挥作用的。例如，钓鱼船是用来钓鱼和游泳的工具，而竹筏和竹马则用于在水下游泳或钓鱼。\n\n我还需要确保语言简洁明了，结构清晰，先总述然后分点详细说明。可能还要提到其他相关物品，比如渔火、渔网等，以增加画面的完整性和丰富性。最后，检查一下是否涵盖了所有主要内容，并且每个物品的用途都明确描述了。\n</think>\n\n这张图片描绘了一幅位于张家嘴河上的自然景观，其中包含了以下几个主要元素：\n\n1. **河流**：河面上漂浮着一些小动物，可能是钓鱼船、竹筏、竹马等。\n2. **小岛**：图中还有一座小岛，周围分布着一些建筑物。\n\n以下是对物品的详细描述：\n\n- **鱼叉**：作为钓鱼的工具，鱼叉可以用来捕捉水中的鱼，进而进行钓鱼活动。\n- **渔火和渔网**：图中的渔火和渔网可能用于维持岸边的自然环境，为钓鱼提供了基本的生活条件。\n- **竹筏和竹马**：这些轻便的交通工具可能被用来在水中游泳或钓鱼，同时也可以作为捕捞工具。\n\n综上所述，这张图片描绘了一幅充满了生动自然景色的画面，其中蕴含着许多与钓鱼相关的物品和活动。"{"$ref":"$.metadata.rateLimit.requestsReset.usage.result.metadata.contentFilters.output.media"}}},"results":[{"$ref":"$.metadata.rateLimit.requestsReset.usage.result"}]}
25-08-22.17:00:08.991 [main            ] INFO  OllamaTest             - Starting OllamaTest using Java 17.0.14 with PID 36236 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.17:00:08.992 [main            ] INFO  OllamaTest             - The following 1 profile is active: "dev"
25-08-22.17:00:10.370 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.17:00:10.372 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.17:00:10.373 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:00:10.565 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.17:00:10.566 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.17:00:10.566 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:00:10.888 [main            ] INFO  OllamaTest             - Started OllamaTest in 2.298 seconds (process running for 3.029)
25-08-22.17:00:12.217 [ForkJoinPool.commonPool-worker-1] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"<think>"{"$ref":"$.media"}}
25-08-22.17:00:12.219 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"\n\n"{"$ref":"$.media"}}
25-08-22.17:00:12.220 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"</think>"{"$ref":"$.media"}}
25-08-22.17:00:12.220 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"\n\n"{"$ref":"$.media"}}
25-08-22.17:00:12.221 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"1"{"$ref":"$.media"}}
25-08-22.17:00:12.221 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":" +"{"$ref":"$.media"}}
25-08-22.17:00:12.221 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":" "{"$ref":"$.media"}}
25-08-22.17:00:12.222 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"1"{"$ref":"$.media"}}
25-08-22.17:00:12.222 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":" equals"{"$ref":"$.media"}}
25-08-22.17:00:12.222 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":" **"{"$ref":"$.media"}}
25-08-22.17:00:12.223 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"2"{"$ref":"$.media"}}
25-08-22.17:00:12.223 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"**"{"$ref":"$.media"}}
25-08-22.17:00:12.224 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"."{"$ref":"$.media"}}
25-08-22.17:00:12.225 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":""{"$ref":"$.media"}}
25-08-22.17:00:12.226 [HttpClient-2-Worker-0] INFO  OllamaTest             - 测试结果(stream): done!
25-08-22.17:00:31.323 [main            ] INFO  OllamaTest             - Starting OllamaTest using Java 17.0.14 with PID 43632 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.17:00:31.325 [main            ] INFO  OllamaTest             - The following 1 profile is active: "dev"
25-08-22.17:00:32.575 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.17:00:32.577 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.17:00:32.577 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:00:32.757 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.17:00:32.758 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.17:00:32.758 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:00:33.076 [main            ] INFO  OllamaTest             - Started OllamaTest in 2.114 seconds (process running for 2.845)
25-08-22.17:00:36.990 [main            ] INFO  HikariDataSource       - HikariPool-1 - Starting...
25-08-22.17:01:34.826 [main            ] INFO  OllamaTest             - Starting OllamaTest using Java 17.0.14 with PID 36584 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.17:01:34.827 [main            ] INFO  OllamaTest             - The following 1 profile is active: "dev"
25-08-22.17:01:36.057 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.17:01:36.060 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.17:01:36.060 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:01:36.244 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.17:01:36.244 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.17:01:36.244 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:01:36.563 [main            ] INFO  OllamaTest             - Started OllamaTest in 2.102 seconds (process running for 2.832)
25-08-22.17:01:39.342 [main            ] INFO  HikariDataSource       - HikariPool-1 - Starting...
25-08-22.17:02:17.440 [main            ] INFO  OllamaTest             - Starting OllamaTest using Java 17.0.14 with PID 39136 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.17:02:17.441 [main            ] INFO  OllamaTest             - The following 1 profile is active: "dev"
25-08-22.17:02:18.746 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.17:02:18.748 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.17:02:18.748 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:02:18.936 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.17:02:18.937 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.17:02:18.937 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:02:19.251 [main            ] INFO  OllamaTest             - Started OllamaTest in 2.172 seconds (process running for 2.942)
25-08-22.17:02:21.927 [main            ] INFO  HikariDataSource       - HikariPool-1 - Starting...
25-08-22.17:02:22.036 [main            ] INFO  HikariPool             - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@42e5f112
25-08-22.17:02:22.037 [main            ] INFO  HikariDataSource       - HikariPool-1 - Start completed.
25-08-22.17:02:22.098 [main            ] INFO  OllamaTest             - 上传完成
25-08-22.17:02:22.116 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariPool-1 - Shutdown initiated...
25-08-22.17:02:22.118 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariPool-1 - Shutdown completed.
25-08-22.17:02:35.581 [main            ] INFO  OllamaTest             - Starting OllamaTest using Java 17.0.14 with PID 23076 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.17:02:35.582 [main            ] INFO  OllamaTest             - The following 1 profile is active: "dev"
25-08-22.17:02:36.859 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.17:02:36.862 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.17:02:36.862 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:02:37.047 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.17:02:37.048 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.17:02:37.048 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.17:02:37.376 [main            ] INFO  OllamaTest             - Started OllamaTest in 2.152 seconds (process running for 2.848)
25-08-22.17:02:38.288 [main            ] INFO  HikariDataSource       - HikariPool-1 - Starting...
25-08-22.17:02:38.422 [main            ] INFO  HikariPool             - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5957f620
25-08-22.17:02:38.424 [main            ] INFO  HikariDataSource       - HikariPool-1 - Start completed.
25-08-22.17:02:50.857 [main            ] INFO  OllamaTest             - 测试结果:{"metadata":{"empty":false,"id":"","model":"deepseek-r1:1.5b","rateLimit":{"requestsLimit":0,"requestsRemaining":0,"requestsReset":{"nano":0,"negative":false,"seconds":0,"units":["SECONDS","NANOS"],"zero":true},"tokensLimit":0,"tokensRemaining":0,"tokensReset":{"$ref":"$.metadata.rateLimit.requestsReset"}},"usage":{"promptTokens":1188,"completionTokens":117,"totalTokens":1305,"generationTokens":117}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"stop"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"messageType":"ASSISTANT"},"text":"<think>\n好吧，我现在要回答这个问题：“人工智能学科始于哪一年”。首先，我需要回忆一下提供的文档内容。根据“DOCUMENTS”，人工智能（Artificial Intelligence，简称AI）的起源可以追溯到20世纪50年代。具体来说，1956年在美国达特茅斯会议上，约翰·麦卡锡提出了人工智能的概念，并将其称为“机器模拟人类每一方面”的理论。\n\n所以，按照提供的资料，人工智能的起点应该是1956年。\n</think>\n\n人工智能学科始于**1956年**。"{"$ref":"$.metadata.rateLimit.requestsReset.usage.result.metadata.contentFilters.output.media"}}},"results":[{"$ref":"$.metadata.rateLimit.requestsReset.usage.result"}]}
25-08-22.17:02:50.876 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariPool-1 - Shutdown initiated...
25-08-22.17:02:50.879 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariPool-1 - Shutdown completed.
25-08-22.18:52:55.079 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 17.0.14 with PID 52672 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.18:52:55.080 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-08-22.18:52:56.535 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.18:52:56.538 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.18:52:56.538 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.18:52:56.749 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.18:52:56.750 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.18:52:56.750 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.18:52:57.073 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 2.384 seconds (process running for 3.19)
25-08-22.18:52:58.827 [main            ] WARN  SpringAiRetryAutoConfiguration - Retry error. Retry count:1
org.springframework.ai.retry.NonTransientAiException: 404 - {"error":{"message":"Invalid URL (POST /v1/chat/completions/v1/chat/completions)","type":"invalid_request_error","code":""}}
	at org.springframework.ai.autoconfigure.retry.SpringAiRetryAutoConfiguration$2.handleError(SpringAiRetryAutoConfiguration.java:100)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:758)
	at org.springframework.ai.openai.api.OpenAiApi.chatCompletionEntity(OpenAiApi.java:257)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$1(OpenAiChatModel.java:274)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$3(OpenAiChatModel.java:274)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiChatModel.internalCall(OpenAiChatModel.java:271)
	at org.springframework.ai.openai.OpenAiChatModel.call(OpenAiChatModel.java:255)
	at xyz.goandrun.knowledge.test.OpenAiTest.test_call(OpenAiTest.java:63)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-08-22.18:53:07.786 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 17.0.14 with PID 53104 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.18:53:07.788 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-08-22.18:53:09.034 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.18:53:09.037 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.18:53:09.037 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.18:53:09.239 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.18:53:09.239 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.18:53:09.239 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.18:53:09.537 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 2.092 seconds (process running for 2.885)
25-08-22.18:53:10.885 [main            ] WARN  SpringAiRetryAutoConfiguration - Retry error. Retry count:1
org.springframework.ai.retry.NonTransientAiException: 404 - {"error":{"message":"Invalid URL (POST /v1/chat/completions/v1/chat/completions)","type":"invalid_request_error","code":""}}
	at org.springframework.ai.autoconfigure.retry.SpringAiRetryAutoConfiguration$2.handleError(SpringAiRetryAutoConfiguration.java:100)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:58)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:826)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$readBody$4(DefaultRestClient.java:815)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:215)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:814)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.lambda$toEntityInternal$2(DefaultRestClient.java:770)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:574)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:758)
	at org.springframework.ai.openai.api.OpenAiApi.chatCompletionEntity(OpenAiApi.java:257)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$1(OpenAiChatModel.java:274)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiChatModel.lambda$internalCall$3(OpenAiChatModel.java:274)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiChatModel.internalCall(OpenAiChatModel.java:271)
	at org.springframework.ai.openai.OpenAiChatModel.call(OpenAiChatModel.java:255)
	at xyz.goandrun.knowledge.test.OpenAiTest.test_call(OpenAiTest.java:63)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.springframework.test.context.junit4.statements.RunBeforeTestExecutionCallbacks.evaluate(RunBeforeTestExecutionCallbacks.java:76)
	at org.springframework.test.context.junit4.statements.RunAfterTestExecutionCallbacks.evaluate(RunAfterTestExecutionCallbacks.java:84)
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75)
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86)
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:231)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)
25-08-22.18:53:45.654 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 17.0.14 with PID 48788 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.18:53:45.655 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-08-22.18:53:47.064 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.18:53:47.066 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.18:53:47.066 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.18:53:47.258 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.18:53:47.258 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.18:53:47.258 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.18:53:47.565 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 2.289 seconds (process running for 3.013)
25-08-22.18:53:55.926 [main            ] INFO  OpenAiTest             - 测试结果(images):{"metadata":{"empty":false,"id":"chatcmpl-C7Jh573dqMwzr40GanXA2PQEde9JZ","model":"gpt-4o-2024-11-20","rateLimit":{"requestsLimit":61060,"requestsRemaining":60678,"tokensLimit":6106000,"tokensRemaining":5752345},"usage":{"promptTokens":280,"completionTokens":135,"totalTokens":415,"generationTokens":135,"nativeUsage":{"completion_tokens":135,"completion_tokens_details":{"accepted_prediction_tokens":0,"audio_tokens":0,"reasoning_tokens":0,"rejected_prediction_tokens":0},"prompt_tokens":280,"prompt_tokens_details":{"audio_tokens":0,"cached_tokens":0},"total_tokens":415}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"refusal":"","finishReason":"STOP","index":0,"id":"chatcmpl-C7Jh573dqMwzr40GanXA2PQEde9JZ","role":"ASSISTANT","messageType":"ASSISTANT"},"text":"这张图片显示了一只卡通风格的狗。狗的主要特征包括棕色的身体、大大的眼睛和稍微张开的嘴巴，表情显得有些惊讶或困惑。它还有一对竖起的耳朵和一条向上翘的尾巴。\n\n这种卡通狗通常用于表情符号或图标，可能被用来表达与狗相关的主题，比如宠物、友谊或忠诚。它也可以用于设计儿童书籍、动画或游戏中的角色，以吸引年轻观众的注意力并增加趣味性。","toolCalls":[]}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.completion_tokens_details.prompt_tokens_details.result"}]}
25-08-22.19:05:58.564 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 17.0.14 with PID 19536 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.19:05:58.565 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-08-22.19:05:59.895 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.19:05:59.898 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.19:05:59.898 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.19:06:00.097 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.19:06:00.098 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.19:06:00.098 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.19:06:00.408 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 2.199 seconds (process running for 3.012)
25-08-22.19:06:03.936 [ForkJoinPool.commonPool-worker-1] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"text":"","toolCalls":[]}
25-08-22.19:06:03.938 [ForkJoinPool.commonPool-worker-1] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"text":"1","toolCalls":[]}
25-08-22.19:06:03.939 [ForkJoinPool.commonPool-worker-1] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"text":" +","toolCalls":[]}
25-08-22.19:06:03.939 [ForkJoinPool.commonPool-worker-1] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"text":" ","toolCalls":[]}
25-08-22.19:06:03.940 [ForkJoinPool.commonPool-worker-1] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"text":"1","toolCalls":[]}
25-08-22.19:06:03.940 [ForkJoinPool.commonPool-worker-1] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"text":" equals","toolCalls":[]}
25-08-22.19:06:03.940 [ForkJoinPool.commonPool-worker-1] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"text":" ","toolCalls":[]}
25-08-22.19:06:04.012 [HttpClient-6-Worker-0] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"text":"2","toolCalls":[]}
25-08-22.19:06:04.014 [HttpClient-6-Worker-0] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"text":".","toolCalls":[]}
25-08-22.19:06:04.046 [HttpClient-6-Worker-0] INFO  OpenAiTest             - 测试结果(stream): {"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"STOP","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JsqB4Y2k3exglB50HuAy8Nh4Tfr","messageType":"ASSISTANT"},"toolCalls":[]}
25-08-22.19:06:04.047 [HttpClient-6-Worker-0] INFO  OpenAiTest             - 测试结果(stream): done!
25-08-22.19:06:09.171 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 17.0.14 with PID 49376 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.19:06:09.172 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-08-22.19:06:10.511 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.19:06:10.514 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.19:06:10.514 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.19:06:10.704 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.19:06:10.705 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.19:06:10.705 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.19:06:11.017 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 2.198 seconds (process running for 2.94)
25-08-22.19:06:12.210 [main            ] INFO  OpenAiTest             - 上传完成
25-08-22.19:06:17.681 [main            ] INFO  OpenAiTest             - Starting OpenAiTest using Java 17.0.14 with PID 49992 (started by ChenSir in E:\Java\ai-mcp-knowledge-20602\ai-mcp-knowledge-app)
25-08-22.19:06:17.683 [main            ] INFO  OpenAiTest             - The following 1 profile is active: "dev"
25-08-22.19:06:19.062 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama_deepseek. Is empty: false
25-08-22.19:06:19.065 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama_deepseek in schema: public
25-08-22.19:06:19.065 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.19:06:19.294 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-08-22.19:06:19.295 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-08-22.19:06:19.295 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-08-22.19:06:19.623 [main            ] INFO  OpenAiTest             - Started OpenAiTest in 2.291 seconds (process running for 3.024)
25-08-22.19:06:22.985 [main            ] INFO  HikariDataSource       - HikariPool-1 - Starting...
25-08-22.19:06:23.100 [main            ] INFO  HikariPool             - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2f0059de
25-08-22.19:06:23.102 [main            ] INFO  HikariDataSource       - HikariPool-1 - Start completed.
25-08-22.19:06:25.649 [main            ] INFO  OpenAiTest             - 测试结果:{"metadata":{"empty":false,"id":"chatcmpl-C7JtB9ScknGS5AnsVylXnv8lkEc5d","model":"gpt-4o-2024-11-20","rateLimit":{"requestsLimit":61060,"requestsRemaining":60715,"tokensLimit":6106000,"tokensRemaining":5768948},"usage":{"promptTokens":68,"completionTokens":10,"totalTokens":78,"generationTokens":10,"nativeUsage":{"completion_tokens":10,"completion_tokens_details":{"accepted_prediction_tokens":0,"audio_tokens":0,"reasoning_tokens":0,"rejected_prediction_tokens":0},"prompt_tokens":68,"prompt_tokens_details":{"audio_tokens":0,"cached_tokens":0},"total_tokens":78}}},"result":{"metadata":{"contentFilters":[],"empty":true,"finishReason":"STOP"},"output":{"media":[],"messageType":"ASSISTANT","metadata":{"finishReason":"STOP","refusal":"","index":0,"role":"ASSISTANT","id":"chatcmpl-C7JtB9ScknGS5AnsVylXnv8lkEc5d","messageType":"ASSISTANT"},"text":"我不知道王大瓜今年几岁。","toolCalls":[]}},"results":[{"$ref":"$.metadata.rateLimit.usage.nativeUsage.completion_tokens_details.prompt_tokens_details.result"}]}
25-08-22.19:06:25.668 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariPool-1 - Shutdown initiated...
25-08-22.19:06:25.670 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariPool-1 - Shutdown completed.
