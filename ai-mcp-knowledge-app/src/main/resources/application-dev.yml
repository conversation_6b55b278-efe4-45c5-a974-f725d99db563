server:
  port: 8090

spring:
  main:
    web-application-type: none
  datasource:
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: postgres
    url: *************************************************
    type: com.zaxxer.hikari.HikariDataSource
  ai:
    openai:
      base-url: https://ai.nengyongai.cn
      api-key: sk-zqoQay9tqXD7PsOY0nCwUW7SCPjTugBJ042IgKKMe7l0kRnD
      embedding-model: text-embedding-ada-002
      embedding:
        options:
          num-batch: 1536
    ollama:
      base-url: http://localhost:11434
      mode:
      embedding-model: nomic-embed-text
      embedding:
        options:
          num-batch: 768

logging:
  level:
    root: info
  config: classpath:logback-spring.xml